# =============================================================================
# DHAN API CONFIGURATION
# =============================================================================
# Your Dhan API access token (required)
DHAN_ACCESS_TOKEN=your_dhan_access_token_here

# Your Dhan client ID (required)
DHAN_CLIENT_ID=your_dhan_client_id_here

# Dhan API base URL (optional, defaults to https://api.dhan.co/v2/marketfeed)
DHAN_BASE_URL=https://api.dhan.co/v2/marketfeed

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Port for the server to run on (optional, defaults to 5000)
PORT=5000

# Server URL for frontend connections (optional)
SERVER_URL=http://localhost:5000

# Node environment (development/production)
NODE_ENV=development

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# MongoDB connection URI (required if using database features)
MONGODB_URI=your_mongodb_connection_string_here

# =============================================================================
# TELEGRAM BOT CONFIGURATIONS
# =============================================================================

# Epicrise bot configuration
TELEGRAM_BOT_TOKEN_EPICRISE=your_epicrise_bot_token_here
TELEGRAM_CHANNEL_ID_EPICRISE=your_epicrise_channel_id_here

# Flash45 bot configuration
TELEGRAM_BOT_TOKEN_FLASH45=your_flash45_bot_token_here
TELEGRAM_CHANNEL_ID_FLASH45=your_flash45_channel_id_here

# QuickFlip bot configuration
TELEGRAM_BOT_TOKEN_QUICKFLIP=your_quickflip_bot_token_here
TELEGRAM_CHANNEL_ID_QUICKFLIP=your_quickflip_channel_id_here

# =============================================================================
# ANGEL ONE API CONFIGURATION (if used)
# =============================================================================
# Angel One client ID (optional, only if using Angel One API)
ANGEL_CLIENT_ID=your_angel_client_id_here

# Angel One password (optional, only if using Angel One API)
ANGEL_PASSWORD=your_angel_password_here

# =============================================================================
# CSV FILE CONFIGURATION
# =============================================================================
# Path to the CSV file containing security data (optional, defaults to newScript.csv)
CSV_FILE_PATH=newScript.csv

# Manual expiry date for options (optional, defaults to 2025-06-19)
MANUAL_EXPIRY=2025-06-19

# Underlying symbol to filter (optional, defaults to NIFTY)
UNDERLYING_SYMBOL=NIFTY
