const getCredentials = require("../../../../cred");
const axios = require("axios");

const {
  BUY_ADJUSTMENT,
  SELL_ADJUSTMENT,
  TRIGGER_ABOVE,
  STOPLOSS_ABOVE,
  TRIGGER_BELOW,
  STOPLOSS_BELOW,
} = require("../../Utils/utilities");
// Helper functions for order placement
async function handleClientOrder(
  client,
  document,
  price,
  transactionType,
  credentials
) {
  const { clientName, authToken, apiKey, capital, userId } = client;

  // console.log(clientName, authToken, apiKey, capital, userId);
  if (!authToken || !apiKey || !capital) {
    console.log(
      `Skipping client ${clientName} due to missing data for big surge`
    );
    return;
  }

  /////////// angel transaction

  const adjustedPrice = adjustPriceForTransaction(price, transactionType);
  // console.log(`Adjusted price for client ${clientId}:`, adjustedPrice);

  const stoploss = price * 0.975; // 2.5% below the price
  const quantity = Math.floor(capital / adjustedPrice);

  const orderData = createOrderData(
    document,
    transactionType,
    adjustedPrice,
    quantity
  );
  // console.log(
  //   // `Primary order data for client ${clientId}:`,
  //   orderData
  // );

  try {
    await placeOrder(orderData, credentials, authToken, apiKey, userId);
    console.log(`Primary order placed for client: ${clientName}`);

    setTimeout(async () => {
      const stopLossOrderData = createStopLossOrderData(
        document,
        transactionType,
        price,
        stoploss,
        quantity
      );
      // console.log(
      //   //   `Stop-loss order data for client ${clientId}:`,
      //   stopLossOrderData
      // );
      await placeOrder(
        stopLossOrderData,
        credentials,
        authToken,
        apiKey,
        userId
      );
      console.log(`Stop-loss order placed for client: ${clientName}`);
    }, 5000);
  } catch (error) {
    console.error(`Error placing order for client ${clientName}:`, error);
  }
}

// Function to adjust price based on transaction type
function adjustPriceForTransaction(price, transactionType) {
  if (transactionType.toUpperCase() === "BUY") {
    return price * BUY_ADJUSTMENT;
  } else if (transactionType.toUpperCase() === "SELL") {
    return price * SELL_ADJUSTMENT;
  }

  throw new Error("Invalid transaction type");
}

// Function to create order data
function createOrderData(document, transactionType, price, quantity) {
  const token = Number(document.token);
  return {
    exchange: "NSE",
    symboltoken: token, // Use the symbol token from the document.token,
    buyorsell: transactionType.toUpperCase(),
    ordertype: "LIMIT",
    producttype: "VALUEPLUS",
    orderduration: "DAY",
    price: Number(roundToTwoDecimalsEndingInZero(price)),
    quantityinlot: quantity,
    amoorder: "N",
  };
}

// Function to create stop-loss order data
function createStopLossOrderData(
  document,
  transactionType,
  messagePrice,
  stopLossPrice,
  quantity
) {
  const token = Number(document.token);
  const reversedTransactionType =
    transactionType.toUpperCase() === "BUY" ? "SELL" : "BUY";

  let triggerPrice;
  let stopLossOrderPrice;

  if (transactionType.toUpperCase() === "SELL") {
    sellOrderPrice = messagePrice * SELL_ADJUSTMENT;
    triggerPrice = messagePrice * TRIGGER_ABOVE;
    stopLossOrderPrice = messagePrice * STOPLOSS_ABOVE;
  } else {
    buyOrderPrice = messagePrice * BUY_ADJUSTMENT;
    triggerPrice = messagePrice * TRIGGER_BELOW;
    stopLossOrderPrice = messagePrice * STOPLOSS_BELOW;
  }
  const roundedStopLossPrice =
    roundToTwoDecimalsEndingInZero(stopLossOrderPrice);
  const roundedTriggerPrice = roundToTwoDecimalsEndingInZero(triggerPrice);

  return {
    exchange: "NSE",
    symboltoken: token, // Use the symbol token from the document.token,
    buyorsell: reversedTransactionType,
    ordertype: "STOPLOSS",
    producttype: "VALUEPLUS",
    orderduration: "DAY",
    price: Number(roundToTwoDecimalsEndingInZero(roundedStopLossPrice)), // Use the calculated stop loss price
    triggerprice: Number(roundToTwoDecimalsEndingInZero(roundedTriggerPrice)), // Round trigger price to two decimal places
    quantityinlot: quantity,
    amoorder: "N",
  };
}

// Round function for stop loss prices
function roundToTwoDecimalsEndingInZero(value) {
  return (Math.round(value * 10) / 10).toFixed(1) + "0";
}

// Function to place order via API
async function placeOrder(orderData, credentials, authToken, apiKey, userId) {
  const config = {
    method: "post",
    url: "https://openapi.motilaloswal.com/rest/trans/v1/placeorder",
    headers: {
      Accept: "application/json",
      "User-Agent": "MOSL/V.1.1.0",
      Authorization: authToken,
      ApiKey: apiKey,
      ClientLocalIp: credentials.localIp,
      ClientPublicIp: credentials.publicIp,
      MacAddress: credentials.macAddress,
      SourceId: "WEB",
      vendorinfo: userId,
      osname: "Windows-10",
      osversion: "10.0.19041",
      devicemodel: "AHV",
      manufacturer: "DELL",
      productname: "Dellserver",
      productversion: "m3-48vcpu-384gb-intel",
      installedappid: "AppID",
      browsername: "Chrome",
      browserversion: "105.0",
    },
    data: JSON.stringify(orderData),
  };

  try {
    const response = await axios(config);
    console.log("Order placed successfully:", response.data);
  } catch (error) {
    console.error("Error placing order:", error);
    throw error;
  }
}

module.exports = {
  placeOrder,
  createStopLossOrderData,
  createOrderData,
  handleClientOrder,
};
