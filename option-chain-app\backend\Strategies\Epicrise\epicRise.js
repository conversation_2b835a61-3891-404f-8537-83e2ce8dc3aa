const express = require("express");
const crypto = require("crypto");
const router = express.Router();

const MotilalRouter = require("./Brokers/MotilalOswal/Motilal.js");
const AngelRouter = require("./Brokers/AngelOne/Angel.js");
const TelegramRouter = require("./Utils/telegram.js");

const {
  createFakeResponse,
  sendMessageToTelegram,
} = require("./Utils/utilities.js");

// // Function to forward request internally
// // const forwardRequest = (req, webhookData, url) => {
// //   return new Promise((resolve, reject) => {
// //     console.log(`Forwarding request to ${url}`); // Debug log
// //     const fakeReq = { ...req, url, method: "POST", body: webhookData };
// //     const fakeRes = createFakeResponse(resolve);

// //     const timeout = setTimeout(
// //       () => reject(new Error(`Timeout forwarding to ${url}`)),
// //       5000
// //     ); // Timeout after 5 seconds

// //     req.app.handle(fakeReq, fakeRes, () => {
// //       clearTimeout(timeout);
// //       resolve({ success: true });
// //     });
// //   });
// // };

// // router.post("/", async (req, res) => {
// //   console.log("Route: /Epicrise");
// //   console.log(req.body);

// //   try {
// //     const webhookData = req.body; // Extract request body

// //     // Forward the request to the other internal routes

// //     const results = await Promise.allSettled([
// //       forwardRequest(req, webhookData, "/Epicrise/MotilalOswal"),
// //       forwardRequest(req, webhookData, "/Epicrise/AngelOne"),
// //     ]);

// //     // Check for failures and log specifics
// //     const hasErrors = results.some((result) => result.status === "rejected");

// //     if (hasErrors) {
// //       return res.status(500).json({
// //         error: "Failed to forward some requests",
// //         details: results.filter((result) => result.status === "rejected"),
// //       });
// //     }

// //     const messageText = req.body;
// //     // Telegram notification
// //     const telegramPromise = sendMessageToTelegram(
// //       CONFIG.EPICRISE.TELEGRAM_BOT_TOKEN,
// //       CONFIG.EPICRISE.CHANNEL_ID,
// //       messageText
// //     );

// //     // Wait for the telegram response
// //     const telegramResponse = await telegramPromise;

// //     // Check the Telegram response
// //     if (!telegramResponse.ok) {
// //       console.log("Error: Failed to send message to Telegram");
// //       return res
// //         .status(500)
// //         .json({ error: "Failed to send message to Telegram" });
// //     }

// //     // Send success response
// //     res.status(200).json({
// //       message:
// //         "Webhook received, forwarded, and notification sent successfully",
// //     });
// //   } catch (error) {
// //     console.error("Error forwarding webhook:", error.message);
// //     if (!res.headersSent) {
// //       res.status(500).json({ error: "Failed to forward webhook" });
// //     }
// //   }
// // });

// const crypto = require("crypto");

// const requestCache = new Map();

// const forwardRequest = (req, webhookData, url) => {
//   return new Promise((resolve, reject) => {
//     console.log(`Forwarding request to ${url}`);

//     // Generate a hash of the request body to identify duplicates
//     const requestHash = crypto
//       .createHash("sha256")
//       .update(JSON.stringify(webhookData))
//       .digest("hex");

//     // Check if the request has already been processed
//     if (requestCache.has(requestHash)) {
//       console.log(
//         `Duplicate request detected for ${url}, skipping forwarding.`
//       );
//       return resolve({
//         success: false,
//         message: "Duplicate request, not forwarded",
//       });
//     }

//     // Store request in cache and set auto-expiry for 1 hour
//     requestCache.set(requestHash, Date.now());
//     setTimeout(() => requestCache.delete(requestHash), 60 * 60 * 1000); // 1 hour expiry

//     // Create a fake request to internally forward the webhook
//     const fakeReq = { ...req, url, method: "POST", body: webhookData };
//     const fakeRes = createFakeResponse(resolve);

//     const timeout = setTimeout(() => {
//       requestCache.delete(requestHash); // Remove from cache if it fails
//       reject(new Error(`Timeout forwarding to ${url}`));
//     }, 2000); // Reduced timeout to 2 seconds

//     req.app.handle(fakeReq, fakeRes, () => {
//       clearTimeout(timeout);
//       resolve({ success: true });
//     });
//   });
// };

// router.post("/", async (req, res) => {
//   console.log("Route: /Epicrise");
//   console.log(req.body);

//   try {
//     const webhookData = req.body; // Extract request body

//     // Forward the request to the other internal routes
//     const results = await Promise.allSettled([
//       forwardRequest(req, webhookData, "/Epicrise/MotilalOswal"),
//       forwardRequest(req, webhookData, "/Epicrise/AngelOne"),
//       forwardRequest(req, webhookData, "/Epicrise/Telegram"),
//     ]);

//     // Check for failures and log specifics
//     const hasErrors = results.some((result) => result.status === "rejected");

//     if (hasErrors) {
//       return res.status(500).json({
//         error: "Failed to forward some requests",
//         details: results.filter((result) => result.status === "rejected"),
//       });
//     }

//     // Send success response
//     res.status(200).json({
//       message: "Webhook received and forwarded successfully",
//     });
//   } catch (error) {
//     console.error("Error forwarding webhook:", error.message);
//     if (!res.headersSent) {
//       res.status(500).json({ error: "Failed to forward webhook" });
//     }
//   }
// });

// // Correctly using the imported MotilalRouter here for /MOEpicrise route
// router.use("/MotilalOswal", MotilalRouter);
// router.use("/AngelOne", AngelRouter);

// router.use("/Telegram", TelegramRouter);
// module.exports = router;

const requestCache = new Map(); // Stores hashes of processed requests

const forwardRequest = (req, webhookData, url) => {
  return new Promise((resolve, reject) => {
    try {
      console.log(`Forwarding request to ${url}`);

      // Create a fake request to internally forward the webhook
      const fakeReq = { ...req, url, method: "POST", body: webhookData };
      const fakeRes = createFakeResponse(resolve);

      const timeout = setTimeout(() => {
        reject(new Error(`Timeout forwarding to ${url}`));
      }, 3000); // 3 seconds timeout

      req.app.handle(fakeReq, fakeRes, () => {
        clearTimeout(timeout);
        resolve({ success: true });
      });
    } catch (error) {
      reject(new Error(`Error forwarding to ${url}: ${error.message}`));
    }
  });
};

router.post("/", async (req, res) => {
  console.log("Route: /Epicrise");
  console.log(req.body);

  try {
    const webhookData = req.body;

    // Generate a hash of the request body to identify duplicates
    const requestHash = crypto
      .createHash("sha256")
      .update(JSON.stringify(webhookData))
      .digest("hex");

    // If the request was already processed within an hour, do NOT forward
    if (requestCache.has(requestHash)) {
      console.log(
        "Duplicate request detected, skipping forwarding to all routes."
      );
      return res.status(200).json({
        message: "Duplicate request detected. No forwarding was done.",
      });
    }

    // Store request hash and auto-expire after 1 hour
    requestCache.set(requestHash, Date.now());
    setTimeout(() => requestCache.delete(requestHash), 60 * 60 * 1000); // 1 hour expiry

    // Forward the request to all three routes
    const results = await Promise.allSettled([
      forwardRequest(req, webhookData, "/Epicrise/MotilalOswal"),
      forwardRequest(req, webhookData, "/Epicrise/AngelOne"),
      forwardRequest(req, webhookData, "/Epicrise/Telegram"),
    ]);

    // Check for failures
    const failedRequests = results.filter(
      (result) => result.status === "rejected"
    );

    if (failedRequests.length > 0) {
      console.error("Some requests failed:", failedRequests);
      return res.status(500).json({
        error: "Failed to forward some requests",
        details: failedRequests.map((result) => ({
          status: result.status,
          reason: result.reason ? result.reason.message : "Unknown error",
        })),
      });
    }

    res.status(200).json({
      message: "Webhook received and forwarded successfully",
    });
  } catch (error) {
    console.error("Error forwarding webhook:", error.message);
    if (!res.headersSent) {
      res.status(500).json({ error: "Failed to forward webhook" });
    }
  }
});

// Attach child routes
router.use("/MotilalOswal", MotilalRouter);
router.use("/AngelOne", AngelRouter);
router.use("/Telegram", TelegramRouter);

module.exports = router;
