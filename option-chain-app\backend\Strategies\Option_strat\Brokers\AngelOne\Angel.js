const express = require("express");

const router = express.Router();
const { EangelparseMessageText } = require("../../Utils/utilities");
const getCredentials = require("./cred");
const { findSymbolInDatabase } = require("../../../../newdb");
const Angeluser = require("../../../../models/Angeluser");
const { angelhandleClientOrder } = require("./AngelUtils");

router.post("/", async (req, res) => {
  console.log("Route: /angelEpicrise");

  let messageText = req.body;
  console.log("Received request body:", messageText);

  // Extract messageText if it's wrapped in an object
  if (typeof messageText === "object" && messageText.messageText) {
    messageText = messageText.messageText;
  }

  // Validate that messageText exists
  if (!messageText) {
    console.log("Error: Message text is required");
    return res.status(400).json({ error: "Message text is required" });
  }

  const parsedData = EangelparseMessageText(messageText);
  console.log("Parsed message text:", parsedData);

  // Validate parsed data
  if (!parsedData) {
    console.log("Error: Invalid message format");
    return res.status(400).json({ error: "Invalid message format" });
  }

  const { symbol, price, transactionType } = parsedData;

  try {
    // Fetch credentials
    const credentials = await getCredentials();
    if (
      !credentials.macAddress ||
      !credentials.localIp ||
      !credentials.publicIp
    ) {
      console.log("Error: Missing required credentials");
      return res.status(400).json({ error: "Missing required credentials" });
    }

    // Find the symbol in the database
    const document = await findSymbolInDatabase(symbol);
    if (!document) {
      console.log("Error: Symbol not found in database");
      return res.status(404).json({ error: "Symbol not found in database" });
    }

    // Fetch all clients from the database
    const clients = await Angeluser.find();

    // Place orders for each client asynchronously
    console.log("Placing orders for each client...");
    const ordersPromises = clients.map((client) =>
      angelhandleClientOrder(
        client,
        document,
        price,
        transactionType,
        credentials
      )
    );

    await Promise.all(ordersPromises);
    console.log("All orders placed.");

    res.json({
      message:
        "Orders placed for all angel clients, stop-loss orders will be placed shortly.",
    });
  } catch (error) {
    console.error("Error handling /Epicrise request:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

module.exports = router;
