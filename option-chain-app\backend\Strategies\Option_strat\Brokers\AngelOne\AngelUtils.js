const axios = require("axios");
const getCredentials = require("../../../../cred");
// const {
//   BUY_ADJUSTMENT,
//   SELL_ADJUSTMENT,
//   TRIGGER_ABOVE,
//   STOPLOSS_ABOVE,
//   TRIGGER_BELOW,
//   STOPLOSS_BELOW,
// } = require("../../Utils/utilities");

async function angelhandleClientOrder(
  client,
  document,
  price,
  transactionType,
  credentials
) {
  const { clientName, jwtToken, apiKey, capital } = client;
  if (!jwtToken || !apiKey || !capital) {
    console.log(`Skipping client ${clientName} due to missing data`);
    return;
  }

  // const adjustedPrice = adjustPriceForTransaction(price, transactionType);
  // // console.log(`Adjusted price for client ${clientId}:`, adjustedPrice);

  // const stoploss = price * 0.975; // 2.5% below the price
  // const quantity = Math.floor(capital / adjustedPrice);

  // const orderData = angelcreateOrderData(
  //   document,
  //   transactionType,
  //   adjustedPrice,
  //   quantity
  // );
  // console.log(
  //   // `Primary order data for client ${clientId}:`,
  //   orderData
  // );

  try {
    await angelplaceOrder(orderData, credentials, jwtToken, apiKey);
    console.log(`Primary order placed for client: ${clientName}`);

    setTimeout(async () => {
      const stopLossOrderData = angelcreateStopLossOrderData(
        document,
        transactionType,
        price,
        stoploss,
        quantity
      );
      // console.log(
      //   //   `Stop-loss order data for client ${clientId}:`,
      //   stopLossOrderData
      // );
      await angelplaceOrder(stopLossOrderData, credentials, jwtToken, apiKey);
      console.log(`Stop-loss order placed for client: ${clientName}`);
    }, 5000);
  } catch (error) {
    console.error(`Error placing order for client ${clientName}:`, error);
  }
}

// Function to adjust price based on transaction type
// function adjustPriceForTransaction(price, transactionType) {
//   if (transactionType.toUpperCase() === "BUY") {
//     return price * BUY_ADJUSTMENT;
//   } else if (transactionType.toUpperCase() === "SELL") {
//     return price * SELL_ADJUSTMENT;
//   }

//   throw new Error("Invalid transaction type");
// }

// Function to create order data
function angelcreateOrderData(document, transactionType, price, quantity) {
  return {
    variety: "NORMAL",
    tradingsymbol: document.symbol,
    symboltoken: document.token,
    transactiontype: transactionType.toUpperCase(),
    exchange: "NFO",
    ordertype: "",
    producttype: "INTRADAY",
    duration: "DAY",
    price: roundToTwoDecimalsEndingInZero(price),
    quantity,
  };
}

// Function to create stop-loss order data
function angelcreateStopLossOrderData(
  document,
  transactionType,
  messagePrice,
  stopLossPrice,
  quantity
) {
  const reversedTransactionType =
    transactionType.toUpperCase() === "BUY" ? "SELL" : "BUY";

  let triggerPrice;
  let stopLossOrderPrice;

  if (transactionType.toUpperCase() === "SELL") {
    sellOrderPrice = messagePrice * SELL_ADJUSTMENT;
    triggerPrice = messagePrice * TRIGGER_ABOVE;
    stopLossOrderPrice = messagePrice * STOPLOSS_ABOVE;
  } else {
    buyOrderPrice = messagePrice * BUY_ADJUSTMENT;
    triggerPrice = messagePrice * TRIGGER_BELOW;
    stopLossOrderPrice = messagePrice * STOPLOSS_BELOW;
  }

  const roundedStopLossPrice =
    roundToTwoDecimalsEndingInZero(stopLossOrderPrice);
  const roundedTriggerPrice = roundToTwoDecimalsEndingInZero(triggerPrice);

  return {
    variety: "STOPLOSS",
    tradingsymbol: document.symbol,
    symboltoken: document.token,
    transactiontype: reversedTransactionType,
    exchange: "NSE",
    ordertype: "STOPLOSS_LIMIT",
    producttype: "INTRADAY",
    duration: "DAY",
    price: roundedStopLossPrice, // Stop-loss price
    triggerprice: roundedTriggerPrice, // Trigger price
    quantity,
  };
}

// Round function for stop loss prices
function roundToTwoDecimalsEndingInZero(value) {
  return (Math.round(value * 10) / 10).toFixed(1) + "0";
}

// Function to place order via API
async function angelplaceOrder(orderData, credentials, jwtToken, apiKey) {
  const config = {
    method: "post",
    url: "https://apiconnect.angelone.in/rest/secure/angelbroking/order/v1/placeOrder",
    headers: {
      Authorization: `Bearer ${jwtToken}`,
      "Content-Type": "application/json",
      Accept: "application/json",
      "X-UserType": "USER",
      "X-SourceID": "WEB",
      "X-ClientLocalIP": credentials.localIp,
      "X-ClientPublicIP": credentials.publicIp,
      "X-MACAddress": credentials.macAddress,
      "X-PrivateKey": apiKey,
    },
    data: JSON.stringify(orderData),
  };

  try {
    const response = await axios(config);
    console.log("Order placed successfully:", response.data);
  } catch (error) {
    console.error("Error placing order:", error);
    throw error;
  }
}

module.exports = {
  angelhandleClientOrder,
  angelcreateOrderData,
  angelcreateStopLossOrderData,
};
