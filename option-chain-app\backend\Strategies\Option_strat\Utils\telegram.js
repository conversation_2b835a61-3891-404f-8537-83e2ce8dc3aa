const express = require("express");
const { sendMessageToTelegram } = require("./utilities.js");
const CONFIG = require("./config.js");
const router = express.Router();

router.post("/", async (req, res) => {
  console.log("Route: /Epicrise/telegram");
  console.log(req.body);

  try {
    const messageText = req.body;

    // Send Telegram notification
    const telegramResponse = await sendMessageToTelegram(
      CONFIG.EPICRISE.TELEGRAM_BOT_TOKEN,
      CONFIG.EPICRISE.CHANNEL_ID,
      messageText
    );

    // Check the Telegram response
    if (!telegramResponse.ok) {
      console.log("Error: Failed to send message to Telegram");
      return res
        .status(500)
        .json({ error: "Failed to send message to Telegram" });
    }

    // Success response
    res.status(200).json({
      message: "Telegram notification sent successfully",
    });
  } catch (error) {
    console.error("Error sending Telegram message:", error.message);
    if (!res.headersSent) {
      res.status(500).json({ error: "Failed to send Telegram message" });
    }
  }
});

module.exports = router;
