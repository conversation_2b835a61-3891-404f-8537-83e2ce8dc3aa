const axios = require("axios");

function createFakeResponse(resolve) {
  return {
    status: () => ({
      json: resolve,
      send: resolve,
    }),
    json: resolve,
    send: resolve,
    end: resolve,
  };
}

async function sendMessageToTelegram(botToken, channelId, messageText) {
  try {
    if (!botToken || !channelId) {
      const errorMsg =
        "Missing Telegram credentials - botToken or channelId is not provided.";
      console.error("Error:", errorMsg);
      return { ok: false, error: errorMsg };
    }

    const response = await axios.post(
      `https://api.telegram.org/bot${botToken}/sendMessage`,
      {
        chat_id: channelId,
        text: messageText,
        parse_mode: "Markdown",
      }
    );

    return response.data;
  } catch (error) {
    let errorDetails = {
      message: "Unknown error occurred",
      rawResponse: null,
    };

    if (error.response) {
      // Telegram API responded with an error
      errorDetails = {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        rawResponse: error.response, // Full raw response object
      };
    } else if (error.request) {
      // No response received (network issue, timeout, etc.)
      errorDetails = {
        message: "No response received from Telegram API",
        rawResponse: error.request, // Full raw request object
      };
    } else {
      // Other unexpected errors
      errorDetails = {
        message: error.message,
        rawResponse: error, // Full raw error object
      };
    }

    console.error("Telegram API Error:", JSON.stringify(errorDetails, null, 2));

    return { ok: false, error: errorDetails };
  }
}

function EangelparseMessageText(messageText) {
  const regex = /ER (Buy|Sell) (\w+) at (\d+(\.\d+)?)/; // Modified regex to also capture price and transaction type
  const match = messageText.match(regex);
  if (match) {
    return {
      transactionType: match[1], // Capture the transaction type (Buy/Sell)
      symbol: match[2], // Capture the symbol
      price: parseFloat(match[3]), // Capture the price
    };
  }
  return null;
}

const BUY_ADJUSTMENT = 1.0025; // 0.25% increase for buy orders
const SELL_ADJUSTMENT = 0.9975; // 0.25% decrease for sell orders
const TRIGGER_ABOVE = 1.025; // 2.50% above for sell orders
const STOPLOSS_ABOVE = 1.0275; // 2.75% above for sell orders
const TRIGGER_BELOW = 0.975; // 2.50% below for buy orders
const STOPLOSS_BELOW = 0.9725; // 2.75% below for buy orders

///////// All changes  are done successfully
module.exports = {
  createFakeResponse,
  sendMessageToTelegram,
  EangelparseMessageText,
  /// PRICES EDJUSTMENT VARIABLES//
  BUY_ADJUSTMENT,
  SELL_ADJUSTMENT,
  TRIGGER_ABOVE,
  STOPLOSS_ABOVE,
  TRIGGER_BELOW,
  STOPLOSS_BELOW,
};
