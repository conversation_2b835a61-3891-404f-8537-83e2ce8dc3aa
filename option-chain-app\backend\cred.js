// credentials.js
const https = require("https");
const os = require("os");
require("dotenv").config();

// Function to get the public IP
function getPublicIp() {
  return new Promise((resolve, reject) => {
    https
      .get("https://api.ipify.org?format=json", (res) => {
        let data = "";
        res.on("data", (chunk) => {
          data += chunk;
        });
        res.on("end", () => {
          const ipInfo = JSON.parse(data);
          resolve(ipInfo.ip); // Resolve with the public IP address
        });
      })
      .on("error", (err) => {
        reject("Error fetching public IP:", err);
      });
  });
}

// Function to get the MAC address
function getMacAddress() {
  const networkInterfaces = os.networkInterfaces();

  for (const interfaceName in networkInterfaces) {
    const networkInfo = networkInterfaces[interfaceName];

    for (const info of networkInfo) {
      if (info.mac && info.mac !== "00:00:00:00:00:00") {
        return info.mac;
      }
    }
  }

  return null; // If no valid MAC address is found
}

// Enhanced function to get the local IP address
function getLocalIp() {
  const networkInterfaces = os.networkInterfaces();

  for (const interfaceName in networkInterfaces) {
    // Loop through all interfaces (e.g., eth0, wlan0, etc.)
    for (const info of networkInterfaces[interfaceName]) {
      // Check for non-internal (not localhost) and IPv4 addresses
      if (info.family === "IPv4" && !info.internal) {
        return info.address; // Return the first valid non-internal IPv4 address
      }
    }
  }

  return "Local IP not found"; // Return a fallback message if no valid address is found
}

// Function to get JWT Token

// Export the functions to be used in other files
module.exports = async () => {
  const publicIp = await getPublicIp(); // Await public IP resolution
  const macAddress = getMacAddress(); // Synchronous function
  const localIp = getLocalIp(); // Local IP retrieval

  // Return an object with the values
  return {
    publicIp,
    macAddress,
    localIp,
  };
};
