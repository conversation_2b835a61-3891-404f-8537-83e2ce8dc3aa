const WebSocket = require("ws");

// Exchange segment constants (from DhanHQ library)
const IDX = 0;
const NSE = 1;
const NSE_FNO = 2;
const NSE_CURR = 3;
const BSE = 4;
const BSE_FNO = 5;
const BSE_CURR = 6;
const MCX = 7;

// Feed request codes
const Ticker = 15;
const Quote = 17;
const Depth = 19;

class CustomDhanWebSocket {
  constructor(clientId, accessToken, instruments, onConnect, onMessage, onClose, onError) {
    this.clientId = clientId;
    this.accessToken = accessToken;
    this.instruments = instruments || [];
    this.onConnect = onConnect;
    this.onMessage = onMessage;
    this.onClose = onClose;
    this.onError = onError;
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 10000;
  }

  connect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log("❌ Max reconnection attempts reached. WebSocket disabled.");
      return;
    }

    console.log("🔌 Connecting to Dhan WebSocket with correct authentication...");
    
    // Use correct authentication method with query parameters
    const wsUrl = `wss://api-feed.dhan.co?version=2&token=${this.accessToken}&clientId=${this.clientId}&authType=2`;
    
    try {
      this.ws = new WebSocket(wsUrl);
      
      this.ws.on('open', () => {
        console.log("✅ WebSocket connection established!");
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        if (this.onConnect) {
          this.onConnect(this);
        }
      });

      this.ws.on('message', (data) => {
        try {
          const parsedData = this.parseMessage(data);
          if (parsedData && this.onMessage) {
            this.onMessage(this, parsedData);
          }
        } catch (error) {
          console.error("❌ Error parsing WebSocket message:", error.message);
        }
      });

      this.ws.on('close', (code, reason) => {
        console.log(`🔒 WebSocket closed - Code: ${code}, Reason: ${reason}`);
        this.isConnected = false;
        
        if (this.onClose) {
          this.onClose(code, reason);
        }
        
        // Auto-reconnect logic
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++;
          console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${this.reconnectDelay/1000} seconds...`);
          setTimeout(() => this.connect(), this.reconnectDelay);
        }
      });

      this.ws.on('error', (error) => {
        console.error("❌ WebSocket error:", error.message);
        this.isConnected = false;
        
        if (this.onError) {
          this.onError(error);
        }
      });

    } catch (error) {
      console.error("❌ Error creating WebSocket:", error.message);
      this.reconnectAttempts++;
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        setTimeout(() => this.connect(), this.reconnectDelay);
      }
    }
  }

  async subscribe(feedRequestCode, instruments) {
    if (!this.isConnected || !this.ws) {
      console.log("⚠️  WebSocket not connected. Cannot subscribe.");
      return;
    }

    console.log(`📤 Subscribing to ${instruments.length} instruments with feed code ${feedRequestCode}`);
    
    // Convert instruments to the format expected by Dhan API
    const instrumentList = instruments.map(([exchangeSegment, securityId]) => ({
      ExchangeSegment: this.getExchangeSegmentName(exchangeSegment),
      SecurityId: securityId.toString()
    }));

    const subscribeMessage = {
      RequestCode: feedRequestCode,
      InstrumentCount: instrumentList.length,
      InstrumentList: instrumentList
    };

    try {
      this.ws.send(JSON.stringify(subscribeMessage));
      console.log("✅ Subscription message sent successfully");
    } catch (error) {
      console.error("❌ Error sending subscription message:", error.message);
    }
  }

  parseMessage(data) {
    if (data.length < 8) {
      console.log("⚠️  Received message too short for header");
      return null;
    }

    // Parse response header (8 bytes)
    const responseCode = data.readUInt8(0);
    const messageLength = data.readUInt16BE(1);
    const exchangeSegment = data.readUInt8(3);
    const securityId = data.readUInt32BE(4);

    console.log(`📨 Received: Code=${responseCode}, Length=${messageLength}, Exchange=${exchangeSegment}, Security=${securityId}`);

    // Parse ticker data (response code 2)
    if (responseCode === 2 && data.length >= 16) {
      const ltp = data.readFloatBE(8);
      const ltt = data.readUInt32BE(12);
      
      return {
        responseCode,
        exchangeSegment,
        securityId,
        ltp,
        ltt,
        type: 'ticker'
      };
    }

    // Parse previous close data (response code 6)
    if (responseCode === 6 && data.length >= 16) {
      const prevClose = data.readFloatBE(8);
      const prevOI = data.readUInt32BE(12);
      
      return {
        responseCode,
        exchangeSegment,
        securityId,
        prevClose,
        prevOI,
        type: 'prevClose'
      };
    }

    // For other response codes, return basic info
    return {
      responseCode,
      exchangeSegment,
      securityId,
      type: 'unknown'
    };
  }

  getExchangeSegmentName(segmentCode) {
    const segments = {
      [IDX]: "IDX_I",
      [NSE]: "NSE_EQ",
      [NSE_FNO]: "NSE_FNO",
      [NSE_CURR]: "NSE_CURR",
      [BSE]: "BSE_EQ",
      [BSE_FNO]: "BSE_FNO",
      [BSE_CURR]: "BSE_CURR",
      [MCX]: "MCX"
    };
    return segments[segmentCode] || "NSE_FNO";
  }

  disconnect() {
    if (this.ws) {
      console.log("🔒 Disconnecting WebSocket...");
      this.isConnected = false;
      this.ws.close();
      this.ws = null;
    }
  }
}

module.exports = {
  CustomDhanWebSocket,
  IDX,
  NSE,
  NSE_FNO,
  NSE_CURR,
  BSE,
  BSE_FNO,
  BSE_CURR,
  MCX,
  Ticker,
  Quote,
  Depth
};
