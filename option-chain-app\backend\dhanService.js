const axios = require("axios");
const WebSocket = require("ws");

const BASE_URL = "https://api.dhan.co/v2/marketfeed";
const ACCESS_TOKEN = process.env.ACCESS_TOKEN;
const CLIENT_ID = process.env.CLIENT_ID;

async function fetchLTPData() {
  try {
    const response = await axios.post(
      `${BASE_URL}/ltp`,
      { IDX_I: [13] },
      {
        headers: {
          "access-token": ACCESS_TOKEN,
          "client-id": CLIENT_ID,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.IDX_I["13"].last_price;
  } catch (error) {
    console.error("❌ Error fetching LTP:", error.message);
    throw error;
  }
}

function startWebSocket() {
  const ws = new WebSocket("wss://dhanhq-websocket-url");

  ws.on("open", () => console.log("✅ Connected to Dhan WebSocket"));
  ws.on("message", (data) => console.log("📊 Live Update:", data));
  ws.on("close", () => console.log("❌ WebSocket Closed"));
}

module.exports = { fetchLTPData, startWebSocket };
