import React, { useEffect, useState } from "react";

const OptionChain = () => {
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetch("http://localhost:5000/api/optionchain")
      .then((response) => response.json())
      .then((json) => setData(json))
      .catch((err) => setError("Error fetching data: " + err.message));
  }, []);

  if (error) {
    return <p className="text-red-500">{error}</p>;
  }

  if (!data) {
    return <p>Loading...</p>;
  }

  const { last_price, oc } = data;

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Nifty Option Chain</h2>
      <p className="mb-2 font-semibold">
        Spot Price: {last_price?.toFixed(2) ?? "N/A"}
      </p>
      <div className="grid grid-cols-3 gap-4 border-t border-b py-2 text-center font-bold">
        <span>Call (CE)</span>
        <span>Strike Price</span>
        <span>Put (PE)</span>
      </div>
      {oc &&
        Object.entries(oc).map(([strike, optionData]) => {
          const ce = optionData?.ce ?? {};
          const pe = optionData?.pe ?? {};

          return (
            <div
              key={strike}
              className="grid grid-cols-3 gap-4 border-b py-2 text-center"
            >
              <div>
                <p>IV: {(ce.implied_volatility ?? 0).toFixed(2)}</p>
                <p>Last: {ce.last_price ?? 0}</p>
                <p>OI: {ce.oi ?? 0}</p>
              </div>
              <div className="font-bold">{strike}</div>
              <div>
                <p>IV: {(pe.implied_volatility ?? 0).toFixed(2)}</p>
                <p>Last: {pe.last_price ?? 0}</p>
                <p>OI: {pe.oi ?? 0}</p>
              </div>
            </div>
          );
        })}
    </div>
  );
};

export default OptionChain;
