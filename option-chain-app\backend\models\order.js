const mongoose = require("mongoose");

const orderResponseSchema = new mongoose.Schema({
  clientId: String,
  orderType: String,
  routeName: String,
  details: {
    status: Boolean,
    message: String,
    script: String,
    orderid: String,
    uniqueorderid: String,
    response: mongoose.Schema.Types.Mixed, // Storing the complete response
    apiKey: String, // Adding client's API key
    jwtToken: String, // Adding client's JWT token
  },
  createdAt: { type: Date, default: Date.now },
});

const OrderparsedMessage = mongoose.model(
  "OrderparsedMessage",
  orderResponseSchema
);

module.exports = OrderparsedMessage;
