require("dotenv").config();
const express = require("express");
const fs = require("fs");
const csv = require("csv-parser");
const axios = require("axios");
const { DhanFeed, NSE_FNO, Ticker, Quote, Depth } = require("dhanhq");
const WebSocket = require("ws");

const app = express();

// Environment variables
const PORT = process.env.PORT || 5000;
const csvFilePath = process.env.CSV_FILE_PATH || "newScript.csv";
const manualExpiry = process.env.MANUAL_EXPIRY || "2025-06-19";
const ACCESS_TOKEN = process.env.DHAN_ACCESS_TOKEN;
const CLIENT_ID = process.env.DHAN_CLIENT_ID;
const BASE_URL = process.env.DHAN_BASE_URL || "https://api.dhan.co/v2/marketfeed";

// Validate required environment variables
if (!ACCESS_TOKEN || !CLIENT_ID) {
  console.error("❌ Missing required environment variables: DHAN_ACCESS_TOKEN or DHAN_CLIENT_ID");
  process.exit(1);
}

let instruments = [];
let dhanFeed = null;

// ✅ Load securities from CSV
fs.createReadStream(csvFilePath)
  .pipe(csv())
  .on("data", (row) => {
    if (
      row["INSTRUMENT"]?.trim() === "OPTIDX" &&
      row["EXCH_ID"]?.trim() === "NSE" &&
      row["SM_EXPIRY_DATE"]?.trim() === manualExpiry &&
      row["UNDERLYING_SYMBOL"]?.trim() === (process.env.UNDERLYING_SYMBOL || "NIFTY")
    ) {
      instruments.push([NSE_FNO, row["SECURITY_ID"]]);
    }
  })
  .on("end", () => {
    console.log("✅ CSV Loaded. Starting WebSocket...");
    startWebSocket();
  });

// ✅ Start WebSocket connection
function startWebSocket() {
  if (dhanFeed) {
    console.log("🔄 Reconnecting WebSocket...");
    dhanFeed.disconnect();
  }

  console.log("🔌 Connecting to Dhan WebSocket...");
  dhanFeed = new DhanFeed(
    CLIENT_ID,
    ACCESS_TOKEN,
    instruments,
    [Ticker, Quote, Depth],
    onConnect,
    onMessage,
    onClose
  );

  dhanFeed.connect();
}

const onConnect = async (instance) => {
  console.log("✅ Connected to DhanHQ WebSocket");
  await instance.subscribe(Ticker, instruments);
};

const onMessage = async (instance, message) => {
  if (!message) return;
  console.log("📩 Received WebSocket Data:", message);
  broadcastRawData(message);
};

const onClose = async () => {
  console.log("❌ WebSocket disconnected. Reconnecting in 5s...");
  setTimeout(startWebSocket, 5000);
};

// ✅ Express Server
const server = app.listen(PORT, () =>
  console.log(`🚀 Server running on http://localhost:${PORT}`)
);

// ✅ WebSocket Server
const wss = new WebSocket.Server({ server, path: "/ws" });

wss.on("connection", (ws) => {
  console.log("✅ Client Connected to WebSocket");
  ws.send(JSON.stringify({ message: "WebSocket Connection Established" }));
});

wss.on("error", (error) => {
  console.error("❌ WebSocket Server Error:", error);
});

// ✅ Broadcast raw WebSocket data
function broadcastRawData(data) {
  const jsonData = JSON.stringify(data);
  console.log("📡 Broadcasting Data:", jsonData); // Debugging

  wss.clients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(jsonData);
    }
  });
}
