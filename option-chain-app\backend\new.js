require("dotenv").config();
const express = require("express");
const fs = require("fs");
const csv = require("csv-parser");
const axios = require("axios");
const { DhanFeed, NSE_FNO, Ticker, Quote, Depth } = require("dhanhq");
const WebSocket = require("ws");

const app = express();
const PORT = process.env.PORT || 5000;
const csvFilePath = "newScript.csv";
const manualExpiry = "2025-06-19";
const ACCESS_TOKEN =
  "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzQzMDU1OTM4LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwMDIzMjM2OSJ9.3y93T68c-u108idrNhN6JcAO_jTkkVlZFVeGwmTZSuLYLByMaVrzpssmvxxS9D0TiiiopFshiZPDBSH3ogCXXQ";

const CLIENT_ID = "1100232369";
const BASE_URL = "https://api.dhan.co/v2/marketfeed";

let instruments = [];
let dhanFeed = null;

// ✅ Load securities from CSV
fs.createReadStream(csvFilePath)
  .pipe(csv())
  .on("data", (row) => {
    if (
      row["INSTRUMENT"]?.trim() === "OPTIDX" &&
      row["EXCH_ID"]?.trim() === "NSE" &&
      row["SM_EXPIRY_DATE"]?.trim() === manualExpiry &&
      row["UNDERLYING_SYMBOL"]?.trim() === "NIFTY"
    ) {
      instruments.push([NSE_FNO, row["SECURITY_ID"]]);
    }
  })
  .on("end", () => {
    console.log("✅ CSV Loaded. Starting WebSocket...");
    startWebSocket();
  });

// ✅ Start WebSocket connection
function startWebSocket() {
  if (dhanFeed) {
    console.log("🔄 Reconnecting WebSocket...");
    dhanFeed.disconnect();
  }

  console.log("🔌 Connecting to Dhan WebSocket...");
  dhanFeed = new DhanFeed(
    CLIENT_ID,
    ACCESS_TOKEN,
    instruments,
    [Ticker, Quote, Depth],
    onConnect,
    onMessage,
    onClose
  );

  dhanFeed.connect();
}

const onConnect = async (instance) => {
  console.log("✅ Connected to DhanHQ WebSocket");
  await instance.subscribe(Ticker, instruments);
};

const onMessage = async (instance, message) => {
  if (!message) return;
  console.log("📩 Received WebSocket Data:", message);
  broadcastRawData(message);
};

const onClose = async () => {
  console.log("❌ WebSocket disconnected. Reconnecting in 5s...");
  setTimeout(startWebSocket, 5000);
};

// ✅ Express Server
const server = app.listen(PORT, () =>
  console.log(`🚀 Server running on http://localhost:${PORT}`)
);

// ✅ WebSocket Server
const wss = new WebSocket.Server({ server, path: "/ws" });

wss.on("connection", (ws) => {
  console.log("✅ Client Connected to WebSocket");
  ws.send(JSON.stringify({ message: "WebSocket Connection Established" }));
});

wss.on("error", (error) => {
  console.error("❌ WebSocket Server Error:", error);
});

// ✅ Broadcast raw WebSocket data
function broadcastRawData(data) {
  const jsonData = JSON.stringify(data);
  console.log("📡 Broadcasting Data:", jsonData); // Debugging

  wss.clients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(jsonData);
    }
  });
}
