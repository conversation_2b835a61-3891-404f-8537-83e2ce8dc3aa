require("dotenv").config();
const express = require("express");
const fs = require("fs");
const csv = require("csv-parser");
const axios = require("axios");
const { DhanFeed, NSE_FNO, Ticker } = require("dhanhq");
const WebSocket = require("ws");
const cors = require("cors");

const app = express();

// Enable CORS for frontend requests
app.use(cors());

// Environment variables
const PORT = process.env.PORT || 5000;
const csvFilePath = process.env.CSV_FILE_PATH || "newScript.csv";
const manualExpiry = process.env.MANUAL_EXPIRY || "2025-06-19";
const ACCESS_TOKEN = process.env.DHAN_ACCESS_TOKEN;
const CLIENT_ID = process.env.DHAN_CLIENT_ID;
const BASE_URL = process.env.DHAN_BASE_URL || "https://api.dhan.co/v2/marketfeed";

// Validate required environment variables
if (!ACCESS_TOKEN || !CLIENT_ID) {
  console.error("❌ Missing required environment variables: DHAN_ACCESS_TOKEN or DHAN_CLIENT_ID");
  process.exit(1);
}

let allSecurities = [];
let instruments = [];
const optionChain = {};
const securityMap = {};

// **Fetch NIFTY Spot Price**
async function getLTPData() {
  try {
    const response = await axios.post(
      `${BASE_URL}/ltp`,
      { IDX_I: [13] },
      {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          "access-token": ACCESS_TOKEN,
          "client-id": CLIENT_ID,
        },
      }
    );

    const niftySpotPrice = response?.data?.data?.IDX_I?.["13"]?.last_price;
    if (niftySpotPrice !== undefined) {
      console.log(`✅ Live NIFTY Spot Price: ${niftySpotPrice}`);
      filterOptions(niftySpotPrice);
    } else {
      console.error("❌ NIFTY Spot Price not found.");
    }
  } catch (error) {
    console.error("❌ Error fetching LTP data:", error.message);
  }
}

// **Read CSV File**
fs.createReadStream(csvFilePath)
  .pipe(csv())
  .on("data", (row) => {
    if (
      row["INSTRUMENT"]?.trim() === "OPTIDX" &&
      row["EXCH_ID"]?.trim() === "NSE" &&
      row["SM_EXPIRY_DATE"]?.trim() === manualExpiry &&
      row["UNDERLYING_SYMBOL"]?.trim() === (process.env.UNDERLYING_SYMBOL || "NIFTY")
    ) {
      allSecurities.push({
        SecurityId: row["SECURITY_ID"],
        StrikePrice: parseFloat(row["STRIKE_PRICE"]),
        OptionType: row["OPTION_TYPE"].trim(),
      });
    }
  })
  .on("end", () => {
    console.log("✅ CSV Loaded. Fetching LTP...");
    getLTPData();
  });

// **Filter Options**
function filterOptions(niftySpotPrice) {
  const sortedStrikes = [
    ...new Set(allSecurities.map((s) => s.StrikePrice)),
  ].sort((a, b) => a - b);
  const atmStrike = sortedStrikes.reduce((prev, curr) =>
    Math.abs(curr - niftySpotPrice) < Math.abs(prev - niftySpotPrice)
      ? curr
      : prev
  );

  console.log(`📍 Identified ATM Strike: ${atmStrike}`);

  const atmIndex = sortedStrikes.indexOf(atmStrike);
  const selectedStrikes = [
    ...sortedStrikes.slice(Math.max(0, atmIndex - 7), atmIndex),
    atmStrike,
    ...sortedStrikes.slice(atmIndex + 1, atmIndex + 9),
  ];

  const filteredSecurities = allSecurities.filter((s) =>
    selectedStrikes.includes(s.StrikePrice)
  );
  instruments = filteredSecurities.map((s) => [NSE_FNO, s.SecurityId]);

  if (!instruments.length) {
    console.error("❌ No valid instruments found.");
    return;
  }

  console.log("✅ Final Selected Securities:");
  console.table(filteredSecurities);

  filteredSecurities.forEach(({ SecurityId, StrikePrice, OptionType }) => {
    securityMap[SecurityId] = { StrikePrice, OptionType };
    if (!optionChain[StrikePrice]) {
      optionChain[StrikePrice] = { callLTP: "-", putLTP: "-" };
    }
  });

  startWebSocket();
}

// **WebSocket Logging**
const logResponse = (message) => {
  const logEntry = `[${new Date().toISOString()}] ${JSON.stringify(
    message,
    null,
    2
  )}\n`;
  fs.appendFile("market_data_log.json", logEntry, (err) => {
    if (err) console.error("❌ Error logging response:", err);
  });
};

// **WebSocket Handlers**
let dhanFeed = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
const reconnectDelay = 10000; // 10 seconds

const onConnect = async (instance) => {
  console.log("✅ Connected to DhanHQ WebSocket");
  console.log("📊 Subscribing to Instruments:", instruments.length);
  reconnectAttempts = 0; // Reset counter on successful connection
  try {
    await instance.subscribe(Ticker, instruments);
  } catch (error) {
    console.error("❌ Error subscribing to instruments:", error.message);
  }
};

const onMessage = async (instance, message) => {
  if (!message || !message.securityId || !message.ltp) return;
  const { securityId, ltp } = message;
  const securityInfo = securityMap[securityId];
  if (!securityInfo) return;
  const { StrikePrice, OptionType } = securityInfo;

  if (OptionType === "CE") {
    optionChain[StrikePrice].callLTP = ltp;
  } else if (OptionType === "PE") {
    optionChain[StrikePrice].putLTP = ltp;
  }

  logResponse(message);
  broadcastData(); // Send updates to all WebSocket clients
};

const onClose = async () => {
  console.log("❌ WebSocket connection closed.");

  if (reconnectAttempts < maxReconnectAttempts) {
    reconnectAttempts++;
    console.log(`🔄 Attempting to reconnect (${reconnectAttempts}/${maxReconnectAttempts}) in ${reconnectDelay/1000} seconds...`);
    setTimeout(startWebSocket, reconnectDelay);
  } else {
    console.log("❌ Max reconnection attempts reached. WebSocket connection disabled.");
    console.log("💡 Please check your Dhan API credentials and token validity.");
  }
};

function startWebSocket() {
  // Skip WebSocket connection if we've exceeded max attempts
  if (reconnectAttempts >= maxReconnectAttempts) {
    console.log("⚠️  WebSocket connection disabled due to repeated failures.");
    return;
  }

  if (dhanFeed) {
    console.log("🔄 Reconnecting WebSocket...");
    try {
      dhanFeed.disconnect();
    } catch (error) {
      console.error("Error disconnecting previous WebSocket:", error.message);
    }
  }

  console.log("🔌 Starting WebSocket connection...");
  try {
    dhanFeed = new DhanFeed(
      CLIENT_ID,
      ACCESS_TOKEN,
      instruments,
      Ticker,
      onConnect,
      onMessage,
      onClose
    );
    dhanFeed.connect();
  } catch (error) {
    console.error("❌ Error creating DhanFeed WebSocket:", error.message);
    reconnectAttempts++;
    if (reconnectAttempts < maxReconnectAttempts) {
      setTimeout(startWebSocket, reconnectDelay);
    }
  }
}

// **Express API Endpoint**
app.get("/api/option-chain", (req, res) => {
  res.json(optionChain);
});

// **Start the Express Server**
const server = app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
});

// **WebSocket Server**
const wss = new WebSocket.Server({ server, path: "/ws" });

wss.on("connection", (ws) => {
  console.log("✅ WebSocket Client Connected");

  // Send initial option chain data
  ws.send(JSON.stringify(optionChain));

  ws.on("close", () => {
    console.log("❌ WebSocket Client Disconnected");
  });
});

const broadcastData = () => {
  const callData = [];
  const putData = [];

  Object.keys(optionChain).forEach((strikePrice) => {
    const callEntry = Object.entries(securityMap).find(
      ([secId, entry]) =>
        entry.StrikePrice == strikePrice && entry.OptionType === "CE"
    );

    const putEntry = Object.entries(securityMap).find(
      ([secId, entry]) =>
        entry.StrikePrice == strikePrice && entry.OptionType === "PE"
    );

    if (callEntry) {
      callData.push({
        StrikePrice: strikePrice,
        callLTP: optionChain[strikePrice].callLTP,
        SecurityId: callEntry[0], // Extract Security ID
      });
    }

    if (putEntry) {
      putData.push({
        StrikePrice: strikePrice,
        putLTP: optionChain[strikePrice].putLTP,
        SecurityId: putEntry[0], // Extract Security ID
      });
    }
  });

  const jsonData = JSON.stringify({ callData, putData });

  wss.clients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(jsonData);
    }
  });
};
