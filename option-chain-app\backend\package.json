{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "server": "node server.js", "newserver": "node newserver.js", "new": "node new.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.8.1", "body-parser": "^1.20.3", "child_process": "^1.0.2", "cli-table3": "^0.6.5", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dhanhq": "^1.0.6", "dotenv": "^16.4.7", "express": "^4.21.2", "express-session": "^1.18.1", "form-data": "^4.0.1", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "mongodb": "^6.10.0", "mongoose": "^8.8.3", "node-fetch": "^2.7.0", "node-schedule": "^2.1.1", "nodemon": "^3.1.10", "path": "^0.12.7", "session": "^0.1.0", "totp-generator": "^1.0.0", "websocket": "^1.0.35", "ws": "^8.18.3"}}