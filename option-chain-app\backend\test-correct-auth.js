require("dotenv").config();
const WebSocket = require("ws");

const ACCESS_TOKEN = process.env.DHAN_ACCESS_TOKEN;
const CLIENT_ID = process.env.DHAN_CLIENT_ID;

console.log("🔍 Testing Correct Dhan WebSocket Authentication...");
console.log(`   🔑 Client ID: ${CLIENT_ID}`);
console.log(`   🎯 Access Token: ${ACCESS_TOKEN ? ACCESS_TOKEN.substring(0, 50) + '...' : 'NOT SET'}`);

// Correct WebSocket URL with query parameters as per official documentation
const CORRECT_WSS_URL = `wss://api-feed.dhan.co?version=2&token=${ACCESS_TOKEN}&clientId=${CLIENT_ID}&authType=2`;

async function testCorrectAuth() {
  console.log(`\n🔌 Testing WebSocket with correct authentication method...`);
  console.log(`   📡 URL: wss://api-feed.dhan.co?version=2&token=***&clientId=${CLIENT_ID}&authType=2`);
  
  const ws = new WebSocket(CORRECT_WSS_URL);

  const timeout = setTimeout(() => {
    console.log(`   ⏰ Timeout after 15 seconds`);
    ws.terminate();
  }, 15000);

  ws.on('open', () => {
    console.log(`   ✅ Connection opened successfully!`);
    console.log(`   📡 WebSocket state: ${ws.readyState}`);
    
    // Test subscribing to a simple instrument (NIFTY index)
    const subscribeMessage = {
      "RequestCode": 15,  // Ticker data
      "InstrumentCount": 1,
      "InstrumentList": [
        {
          "ExchangeSegment": "IDX_I",
          "SecurityId": "13"  // NIFTY 50 index
        }
      ]
    };
    
    console.log(`   📤 Sending subscription message:`, subscribeMessage);
    ws.send(JSON.stringify(subscribeMessage));
    
    clearTimeout(timeout);
    
    // Keep connection open for a bit to receive data
    setTimeout(() => {
      console.log(`   🔒 Closing connection after successful test`);
      ws.close();
    }, 10000);
  });

  ws.on('error', (error) => {
    console.log(`   ❌ Connection error:`, error.message);
    clearTimeout(timeout);
  });

  ws.on('close', (code, reason) => {
    console.log(`   🔒 Connection closed - Code: ${code}, Reason: ${reason}`);
    clearTimeout(timeout);
  });

  ws.on('message', (data) => {
    console.log(`   📨 Received binary message, length: ${data.length} bytes`);
    
    // Parse the response header (first 8 bytes)
    if (data.length >= 8) {
      const responseCode = data.readUInt8(0);
      const messageLength = data.readUInt16BE(1);
      const exchangeSegment = data.readUInt8(3);
      const securityId = data.readUInt32BE(4);
      
      console.log(`   📊 Response Code: ${responseCode}`);
      console.log(`   📏 Message Length: ${messageLength}`);
      console.log(`   🏢 Exchange Segment: ${exchangeSegment}`);
      console.log(`   🆔 Security ID: ${securityId}`);
      
      // If it's ticker data (response code 2), parse LTP
      if (responseCode === 2 && data.length >= 16) {
        const ltp = data.readFloatBE(8);
        const ltt = data.readUInt32BE(12);
        console.log(`   💰 Last Traded Price: ${ltp}`);
        console.log(`   🕐 Last Trade Time: ${ltt}`);
      }
    }
  });
}

testCorrectAuth();
