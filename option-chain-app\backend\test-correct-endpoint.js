require("dotenv").config();
const WebSocket = require("ws");

const ACCESS_TOKEN = process.env.DHAN_ACCESS_TOKEN;
const CLIENT_ID = process.env.DHAN_CLIENT_ID;

console.log("🔍 Testing Correct Dhan WebSocket Endpoint...");
console.log(`   🔑 Client ID: ${CLIENT_ID}`);
console.log(`   🎯 Access Token: ${ACCESS_TOKEN ? ACCESS_TOKEN.substring(0, 50) + '...' : 'NOT SET'}`);

const CORRECT_WSS_URL = 'wss://api-feed.dhan.co';

async function testCorrectEndpoint() {
  console.log(`\n🔌 Testing WebSocket endpoint: ${CORRECT_WSS_URL}`);
  
  const ws = new WebSocket(CORRECT_WSS_URL, {
    headers: {
      'Authorization': `Bearer ${ACCESS_TOKEN}`,
      'User-Agent': 'DhanHQ-WebSocket-Client',
    }
  });

  const timeout = setTimeout(() => {
    console.log(`   ⏰ Timeout after 15 seconds`);
    ws.terminate();
  }, 15000);

  ws.on('open', () => {
    console.log(`   ✅ Connection opened successfully!`);
    console.log(`   📡 WebSocket state: ${ws.readyState}`);
    
    // Try to send authorization message like the library does
    console.log(`   🔐 Attempting authorization...`);
    
    clearTimeout(timeout);
    
    // Keep connection open for a bit to see if we get any messages
    setTimeout(() => {
      console.log(`   🔒 Closing connection after successful test`);
      ws.close();
    }, 5000);
  });

  ws.on('error', (error) => {
    console.log(`   ❌ Connection error:`, error.message);
    console.log(`   📊 Error details:`, error);
    clearTimeout(timeout);
  });

  ws.on('close', (code, reason) => {
    console.log(`   🔒 Connection closed - Code: ${code}, Reason: ${reason}`);
    clearTimeout(timeout);
  });

  ws.on('message', (data) => {
    console.log(`   📨 Received message:`, data);
  });
}

testCorrectEndpoint();
