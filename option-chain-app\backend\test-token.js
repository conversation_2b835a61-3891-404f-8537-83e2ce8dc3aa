require("dotenv").config();
const axios = require("axios");

const ACCESS_TOKEN = process.env.DHAN_ACCESS_TOKEN;
const CLIENT_ID = process.env.DHAN_CLIENT_ID;
const BASE_URL = process.env.DHAN_BASE_URL || "https://api.dhan.co/v2/marketfeed";

console.log("🔍 Testing Dhan API Token...");
console.log(`   🔑 Client ID: ${CLIENT_ID}`);
console.log(`   🎯 Access Token: ${ACCESS_TOKEN ? ACCESS_TOKEN.substring(0, 50) + '...' : 'NOT SET'}`);

async function testToken() {
  try {
    console.log("\n📡 Testing LTP API...");
    const response = await axios.post(
      `${BASE_URL}/ltp`,
      { IDX_I: [13] },
      {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          "access-token": ACCESS_TOKEN,
          "client-id": CLIENT_ID,
        },
      }
    );

    console.log("✅ LTP API Response:");
    console.log(`   📊 Status: ${response.status}`);
    console.log(`   📝 Data:`, JSON.stringify(response.data, null, 2));

    // Test token expiry by decoding JWT
    if (ACCESS_TOKEN) {
      try {
        const tokenParts = ACCESS_TOKEN.split('.');
        if (tokenParts.length === 3) {
          const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
          console.log("\n🔍 Token Information:");
          console.log(`   📅 Issued by: ${payload.iss}`);
          console.log(`   🆔 Client ID: ${payload.dhanClientId}`);
          console.log(`   ⏰ Expires: ${new Date(payload.exp * 1000).toLocaleString()}`);
          console.log(`   🕐 Current time: ${new Date().toLocaleString()}`);
          
          const isExpired = payload.exp * 1000 < Date.now();
          console.log(`   ⚠️  Token expired: ${isExpired ? '❌ YES' : '✅ NO'}`);
        }
      } catch (e) {
        console.log("❌ Could not decode token:", e.message);
      }
    }

  } catch (error) {
    console.error("❌ API Test Failed:");
    console.error("   Message:", error.message);
    console.error("   Status:", error.response?.status);
    console.error("   Status Text:", error.response?.statusText);
    console.error("   Response Data:", error.response?.data);
  }
}

testToken();
