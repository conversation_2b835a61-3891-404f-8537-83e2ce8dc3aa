require("dotenv").config();
const WebSocket = require("ws");

const ACCESS_TOKEN = process.env.DHAN_ACCESS_TOKEN;
const CLIENT_ID = process.env.DHAN_CLIENT_ID;

console.log("🔍 Testing Direct WebSocket Connection to Dhan...");
console.log(`   🔑 Client ID: ${CLIENT_ID}`);
console.log(`   🎯 Access Token: ${ACCESS_TOKEN ? ACCESS_TOKEN.substring(0, 50) + '...' : 'NOT SET'}`);

// Test different possible WebSocket endpoints
const possibleEndpoints = [
  'wss://api.dhan.co/v2/marketfeed/ws',
  'wss://api.dhan.co/ws',
  'wss://marketfeed.dhan.co/ws',
  'wss://ws.dhan.co/v2/marketfeed',
];

async function testWebSocketEndpoint(url) {
  return new Promise((resolve) => {
    console.log(`\n🔌 Testing WebSocket endpoint: ${url}`);
    
    const ws = new WebSocket(url, {
      headers: {
        'access-token': ACCESS_TOKEN,
        'client-id': CLIENT_ID,
        'User-Agent': 'DhanHQ-WebSocket-Client',
      }
    });

    const timeout = setTimeout(() => {
      console.log(`   ⏰ Timeout after 10 seconds`);
      ws.terminate();
      resolve({ url, status: 'timeout' });
    }, 10000);

    ws.on('open', () => {
      console.log(`   ✅ Connection opened successfully`);
      clearTimeout(timeout);
      ws.close();
      resolve({ url, status: 'success' });
    });

    ws.on('error', (error) => {
      console.log(`   ❌ Connection error:`, error.message);
      clearTimeout(timeout);
      resolve({ url, status: 'error', error: error.message });
    });

    ws.on('close', (code, reason) => {
      console.log(`   🔒 Connection closed - Code: ${code}, Reason: ${reason}`);
      clearTimeout(timeout);
      if (!timeout._destroyed) {
        resolve({ url, status: 'closed', code, reason: reason.toString() });
      }
    });
  });
}

async function testAllEndpoints() {
  console.log("\n🚀 Testing all possible WebSocket endpoints...\n");
  
  for (const endpoint of possibleEndpoints) {
    const result = await testWebSocketEndpoint(endpoint);
    console.log(`   📊 Result: ${result.status}`);
  }
  
  console.log("\n💡 If all endpoints fail with 400 errors, the issue might be:");
  console.log("   1. WebSocket authentication method differs from REST API");
  console.log("   2. Additional headers or parameters required for WebSocket");
  console.log("   3. WebSocket feature not enabled for this token");
  console.log("   4. Different WebSocket URL structure");
}

testAllEndpoints();
